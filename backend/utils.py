import time
import uuid
from urllib.parse import urlparse

import requests
from minio import <PERSON><PERSON>
from yfflow import <PERSON><PERSON><PERSON><PERSON><PERSON>
from yunfu.common import ConfigUtils, SingletonMeta

conf = ConfigUtils.load('conf/config.yaml')
node_color_conf = ConfigUtils.load('conf/node_color.yaml')
logger = YfLogger(__name__)


class MinioUtils(metaclass=SingletonMeta):
    minio = Minio(**conf['minio'])

    @classmethod
    def download(cls, bucket_name: str, object_name: str):
        data = None
        try:
            data = cls.minio.get_object(bucket_name, object_name)
            return data.read()
        except Exception as e:
            err_msg = f'文件下载异常: {object_name} -> {e}'
            logger.error(err_msg)
            raise ValueError(err_msg)
        finally:
            if data:
                data.close()
                data.release_conn()

    @classmethod
    def write(cls, bucket_name, filename, f_in, size=None):
        logger.info(f'Upload to minio {bucket_name}: {filename}')
        if not size:
            f_in.seek(0, 2)
            size = f_in.tell()
            f_in.seek(0, 0)
        cls.minio.put_object(bucket_name, filename, f_in, length=size)


class UrlUtils:

    @staticmethod
    def is_valid(url: str) -> bool:
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except ValueError:
            return False

    @staticmethod
    def download(url: str) -> bytes:    # type: ignore
        try:
            response = requests.get(url)
            return response.content
        except Exception as e:
            logger.error(f'文件下载异常: {url} -> {e}')


class GraphUtils:

    @classmethod
    def get_label(cls, kg_id: int) -> str:
        """
        生成图数据节点标签
        区分线上环境(yunfu_due_kg)和本地开发环境(yunfu_due_kgT)
        :param kg_id: 图谱id
        """
        return f'KG{kg_id}'

    @classmethod
    def get_ontology_label(cls, kg_id: int) -> str:
        """
        生成图数据本体节点标签
        区分线上环境(yunfu_due_kg)和本地开发环境(yunfu_due_kgT)
        :param kg_id: 图谱id
        """
        return 'concept'


class UuidUtils:
    """唯一标识符生成工具"""

    @classmethod
    def get_uuid(cls, text) -> str:
        now = time.time()
        return str(uuid.uuid3(uuid.NAMESPACE_DNS, f'{text}_{now}'))


class NodeColorManager:
    """节点颜色管理器"""
    root_name = '事物'

    @classmethod
    def get_node2color(cls, names):
        # 主要用于生成实体类型对应的颜色，将实体类型名转成数字，除以预置颜色总数求余数，余数对应的颜色列表下标为对应颜色
        colors = node_color_conf['NODE_COLORS']
        color_length = len(colors)
        name2color = {}
        for name in names:
            num = int(str(uuid.uuid3(uuid.NAMESPACE_DNS, name))[:8], base=16)
            org_remainder = (num % color_length)
            color = colors[org_remainder]
            name2color[name] = color
        return name2color


class TimeUtils:
    @classmethod
    def now_str(cls) -> str:
        """
        格式化成2016-03-20 11:45:39形式
        """
        return time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
