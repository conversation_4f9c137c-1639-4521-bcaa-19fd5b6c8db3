from abc import ABC, abstractmethod
from typing import List, Union

from yunfu.db.graph.graph.graph_dbs import NebulaGraphDb, Neo4jGraphDb
from yunfu.db.graph.models import Edge, Node


class BaseGraphMapper(ABC):
    graph_db: Union[Neo4jGraphDb, NebulaGraphDb]

    @abstractmethod
    def insert_node(self, space: str, node: Node) -> Node:
        """插入点"""
        raise NotImplementedError

    @abstractmethod
    def insert_edge(self, space: str, edge: Edge) -> Edge:
        """插入边"""
        raise NotImplementedError

    def get_nodes(self, space: str, skip: int, limit: int, types: List[str] = [], props: list = []) -> List[Node]:
        graph = self.graph_db.get_graph(space)
        return graph.nodes.match(types, props).skip(skip).limit(limit).all()

    def get_node_by_name(
        self, space: str, version: str, types: List[str], name: str
    ) -> Node:
        graph = self.graph_db.get_graph(space)
        if version != "c":
            graph = graph.get_history_graph(version)
        return graph.nodes.match(
            types=types,
            props=[("name", "=", name)],
        ).first()

    def get_nodes_count(self, space) -> int:
        graph = self.graph_db.get_graph(space)
        return graph.nodes.count()

    @abstractmethod
    def get_all_relations_by_node(self, space: str, node: Node) -> List[Edge]:
        raise NotImplementedError

    @abstractmethod
    def export_entities(
        self,
        space: str,
        skip: int = 0,
        limit: int = 20,
        version: str = "c",
    ) -> list:
        """导出实体"""
        raise NotImplementedError

    @abstractmethod
    def export_relations(
        self,
        space: str,
        skip: int = 0,
        limit: int = 20,
        version: str = "c",
    ) -> list:
        """导出关系"""
        raise NotImplementedError

    def get_all_onto_relations_by_labels(self, space, version, types, name):
        graph = self.graph_db.get_graph(space)
        if version != "c":
            graph = graph.get_history_graph(version)
        return graph.edges.match(
            src_node_types=types,
            dst_node_types=types,
            props=[("name", "=", "属于")],
        ).where(dst_node_props=[("name", "=", name)]).all()

    @abstractmethod
    def delete_node_version_by_eid(self, space: str, eid: str, version: str):
        """删除节点版本"""
        raise NotImplementedError

    @abstractmethod
    def delete_relation_version_by_id(self, space: str, id: int, version: str) -> None:
        raise NotImplementedError

    def get_edge_by_node_name(self, space: str, src_node_name: str, dest_node_name: str, relation_name: str):
        graph = self.graph_db.get_graph(space)
        return graph.edges.match(
            src_node_props=[("name", "=", src_node_name)],
            dst_node_props=[("name", "=", dest_node_name)],
            props=[("name", "=", relation_name)],
        ).first()

    def get_all_relations_by_node_with_labels(self, space, src_node_types, src_id, dst_node_types=[]):
        graph = self.graph_db.get_graph(space)
        return graph.edges.match(
            src_node_types=src_node_types,
            dst_node_types=dst_node_types,
            src_node_props=[("eid", "=", src_id)],
        ).all()

    @abstractmethod
    def partial_update_node(self, space: str, node_id: str, props: dict) -> Node:
        raise NotImplementedError

    @abstractmethod
    def delete_node_by_name(self, space: str, types: List[str], name: str):
        raise NotImplementedError

    @abstractmethod
    def delete_node_by_ontology(self, space: str, types: List[str], name: str):
        raise NotImplementedError

    @abstractmethod
    def get_edges_with_eid(self, space: str, labels: List[str], eid: str):
        raise NotImplementedError

    @abstractmethod
    def partial_update_edge(self, space: str, edge_id: str, props: dict) -> Edge:
        raise NotImplementedError

    @abstractmethod
    def create_edge(self, edge: Edge) -> Edge:
        """创建边（兼容旧接口）"""
        raise NotImplementedError

    @abstractmethod
    def save_node(self, node: Node) -> Node:
        """保存节点（兼容旧接口）"""
        raise NotImplementedError

    @abstractmethod
    def sync_entity_type_by_ontology_name(self, space: str, kg_id: int, ontology_labels: list, old_node_name: str, new_property_value: str) -> None:
        """根据本体名称同步实体的_type属性并移除旧标签"""
        raise NotImplementedError

    @abstractmethod
    def sync_entity_property_rename(self, space: str, ontology_labels: list, node_name: str, old_property_name: str, new_property_name: str) -> None:
        """同步实体属性重命名"""
        raise NotImplementedError

    @abstractmethod
    def sync_entity_property_remove(self, space: str, ontology_labels: list, node_name: str, property_name: str) -> None:
        """同步删除实体属性"""
        raise NotImplementedError

    @abstractmethod
    def sync_entity_relation_rename(self, space: str, kg_id: int, src_type: str, dst_type: str, old_relation_name: str, new_relation_name: str) -> None:
        """同步实体关系重命名"""
        raise NotImplementedError

    @abstractmethod
    def sync_entity_relation_remove(self, space: str, kg_id: int, src_type: str, dst_type: str, relation_name: str) -> None:
        """同步删除实体关系（设置c=null）"""
        raise NotImplementedError
