from abc import ABC, abstractmethod
from pathlib import Path
from zipfile import ZipFile

import requests
from yfflow import <PERSON><PERSON><PERSON><PERSON><PERSON>
from yunfu.common import ConfigUtils

from backend.models.file import UserFile

from .models import ExportTypes
from .triple_generator import TripleGenerator

conf = ConfigUtils.load('conf/config.yaml')
logger = YfLogger(__name__)


class BaseExporter(ABC):
    dir_path = 'data/export'                # 临时文件存放目录
    default_suffix = conf['export_file_suffix']['default']     # 正常导出文件后缀
    full_suffix = conf['export_file_suffix']['full']           # 全量导出文件后缀

    @classmethod
    @abstractmethod
    def export(cls, kg_id: int, version: str, org_id: str) -> str:
        """图谱数据导出"""
        raise NotImplementedError

    @classmethod
    def upload_file(cls, tem_file_path: Path, file_name: str, org_id: str) -> str:
        """上传文件到文件索引"""
        with open(tem_file_path, 'rb') as fin:
            file_id = UserFile.upload_file(0, org_id, '/', fin, '', 0, suffix=tem_file_path.suffix.lstrip('.'), file_name=file_name)    # noqa
        tem_file_path.unlink(missing_ok=True)
        return file_id      # noqa


class EntityExporter(BaseExporter):
    """图谱实体数据导出"""

    @classmethod
    def export(cls, kg_id: int, version: str, org_id: str) -> str:
        triples, _ = TripleGenerator.generate(kg_id, version, ExportTypes.ENTITY)
        file_name = f'{kg_id}_{version}_{ExportTypes.ENTITY.value}.{cls.default_suffix}'
        temp_file_path = Path(cls.dir_path) / file_name
        with open(temp_file_path, 'w') as fout:
            for triple in triples:
                fout.write(triple)
        return cls.upload_file(temp_file_path, file_name, org_id)


class OntologyExporter(BaseExporter):
    """图谱本体数据导出"""

    @classmethod
    def export(cls, kg_id: int, version: str, org_id: str) -> str:
        triples, _ = TripleGenerator.generate(kg_id, version, ExportTypes.ONTOLOGY)
        file_name = f'{kg_id}_{version}_{ExportTypes.ONTOLOGY.value}.{cls.default_suffix}'
        temp_file_path = Path(cls.dir_path) / file_name
        with open(temp_file_path, 'w') as fout:
            for triple in triples:
                fout.write(triple)
        return cls.upload_file(temp_file_path, file_name, org_id)


class KgExporter(BaseExporter):
    """图谱数据导出"""

    @classmethod
    def export(cls, kg_id: int, version: str, org_id: str) -> str:
        triples, _ = TripleGenerator.generate(kg_id, version, ExportTypes.KG)
        file_name = f'{kg_id}_{version}_{ExportTypes.KG.value}.{cls.default_suffix}'
        temp_file_path = Path(cls.dir_path) / file_name
        with open(temp_file_path, 'w') as fout:
            for triple in triples:
                fout.write(triple)
        file_id = cls.upload_file(temp_file_path, file_name, org_id)
        requests.post(conf['file_parse_url'], data={'id': file_id, 'org_id': org_id})
        return file_id


class FullExporter(BaseExporter):
    """图谱全量数据导出"""

    @classmethod
    def export(cls, kg_id: int, version: str, org_id: str) -> str:
        entity_triples, entity_medias = TripleGenerator.generate(kg_id, version, ExportTypes.ENTITY, with_media=True)
        ontology_triples, ontology_medias = TripleGenerator.generate(kg_id, version, ExportTypes.ONTOLOGY)
        medias = entity_medias + ontology_medias
        file_name = f'{kg_id}_{version}_{ExportTypes.FULL.value}.{cls.full_suffix}'
        temp_file_path = Path(cls.dir_path) / file_name
        with ZipFile(temp_file_path, 'w') as zip_f:
            zip_f.writestr('entities.triple', b''.join([_.encode('utf-8') for _ in entity_triples]))
            zip_f.writestr('ontology.triple', b''.join([_.encode('utf-8') for _ in ontology_triples]))
            for media in medias:
                zip_f.writestr(media.file_name, media.content)
        return cls.upload_file(temp_file_path, file_name, org_id)


class ExporterFactory:
    type2exporter = {
        ExportTypes.KG: KgExporter,
        ExportTypes.ENTITY: EntityExporter,
        ExportTypes.ONTOLOGY: OntologyExporter,
        ExportTypes.FULL: FullExporter
    }

    @classmethod
    def get_exporter(cls, export_type: ExportTypes) -> BaseExporter:
        return cls.type2exporter.get(export_type)       # type: ignore
