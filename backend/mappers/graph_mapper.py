import datetime
import time
from typing import Dict, List

from elasticsearch_dsl import connections
from yunfu.common import ConfigUtils, SingletonMeta, StatClient

from backend.models.kg import Linker
from yunfu.db.graph.deprecated.graph import Neo4jGraph
from yunfu.db.graph.deprecated.models import Edge, Node
from yunfu.db.graph.deprecated.neo4j_client import GraphDatabase, Neo4jClient

conf = ConfigUtils.load("conf/config.yaml")
stat_client = StatClient(**conf["stat_conf"])


class GraphMapper(Neo4jClient):
    __metaclass__ = SingletonMeta

    def __init__(self, config: Dict) -> None:
        uri = (
            f"{config['db']['schema']}://{config['db']['host']}:{config['db']['port']}"
        )
        auth = (config["db"]["username"], config["db"]["password"])
        self.graph = GraphDatabase.driver(uri, auth=auth)
        self.min_count = config.get("min_count", 5)
        conf = {
            "host": config["db"]["host"],
            "port": config["db"]["port"],
            "username": config["db"]["username"],
            "password": config["db"]["password"],
        }

        self.neo4j_graph = Neo4jGraph(
            conf, space="default", use_version=True, stat_client=stat_client
        )

    def get_nodes_with_labels(
        self,
        labels: List[str],
        limit: int,
        skip: int,
    ) -> List[Node]:
        query = f'MATCH (m:{":".join(labels)})  RETURN m SKIP {skip} LIMIT {limit}'
        results = self.run(query)
        return [self._parse_node(result[0]) for result in results]

    def get_nodes_count(self, labels: List[str]) -> int:
        query = f'MATCH (m:{":".join(labels)})  RETURN COUNT(m)'
        count = self.run(query)
        return count[0][0]

    def export_entity_triples(
        self, label: str, skip: int, limit: int, version: str, export_type: str = "kg"
    ):
        """导出属性三元组

        :param label: 标签
        :type label: str
        :param skip: 跳过参数
        :type skip: int
        :param limit: 限度参数
        :type limit: int
        :return: 查询数据
        :rtype: dict
        """
        print("[export_entity_triples]")
        entities = self.export_entities(label, skip, limit, version, export_type)
        data = []
        filters = {
            "name": True,
            "_create_time": True,
            "_update_time": True,
        }
        for entity in entities:
            triples = [
                [entity["name"], key, entity[key]]
                for key in entity
                if not filters.get(key)
            ]
            data += triples
        total = len(data)
        return {"total": total, "data": data}

    def export_entities(
        self,
        label: str,
        skip: int = 0,
        limit: int = 20,
        version: str = "c",
        export_type: str = "kg",
    ):
        """导出实体

        :param label: 标签
        :type label: str
        :param skip: 跳过参数, defaults to 0
        :type skip: int, optional
        :param limit: 限度参数, defaults to 20
        :type limit: int, optional
        :return: 查询数据
        :rtype: list
        """
        print("[export_entities]")
        if export_type == "ontology":
            query = (
                """MATCH (n:`{0}`:`{3}`:concept) RETURN n SKIP {1} LIMIT {2}""".format(
                    label, skip, limit, version
                )
            )
        elif export_type == "entity":
            query = """MATCH (n:`{0}`:`{3}`) WHERE NOT n:concept RETURN n SKIP {1} LIMIT {2}""".format(
                label, skip, limit, version
            )
        else:
            query = """MATCH (n:`{0}`:`{3}`) RETURN n SKIP {1} LIMIT {2}""".format(
                label, skip, limit, version
            )
        data = self.run(query)
        return [item["n"] for item in data] if data else []

    def export_relation_triples(
        self, label: str, skip: int, limit: int, version: str, export_type: str = "kg"
    ):
        """导出关系三元组

        :param label: 标签
        :type label: str
        :param skip: 跳过参数
        :type skip: int
        :param limit: 限度参数
        :type limit: int
        :return: 关系三元组
        :rtype: dict
        """
        print("[export_relation_triples]")
        relations = self.export_relations(label, skip, limit, version, export_type)
        data = []
        for relation in relations:
            data.append(
                [
                    relation.src_node.name,
                    relation.properties["name"],
                    relation.dest_node.name,
                ]
            )
        total = len(data)
        return {"total": total, "data": data}

    def export_relations(
        self,
        label: str,
        skip: int = 0,
        limit: int = 20,
        version: str = "c",
        export_type: str = "kg",
    ):
        """导出关系

        :param label: 标签
        :type label: str
        :param skip: 跳过参数, defaults to 0
        :type skip: int, optional
        :param limit: 限度参数, defaults to 20
        :type limit: int, optional
        :return: 查询数据
        :rtype: list
        """
        print("[export_relations]")
        if export_type == "ontology":
            query = """MATCH p=(n:`{0}`:`{1}`:concept)-[r]->(m:`{0}`:`{1}`:concept)""".format(
                label, version
            ) + """WHERE r.{2}=true RETURN p SKIP {0} LIMIT {1}""".format(
                skip, limit, version
            )
        elif export_type == "entity":
            query = """MATCH p=(n:`{0}`:`{1}`)-[r]->(m:`{0}`:`{1}`)""".format(
                label, version
            ) + """ WHERE r.{2}=true AND NOT n:concept AND NOT m:concept RETURN p SKIP {0} LIMIT {1}""".format(
                skip, limit, version
            )
        else:
            query = """MATCH p=(n:`{0}`:`{1}`)-[r]->(m:`{0}`:`{1}`)""".format(
                label, version
            ) + """ WHERE r.{2}=true RETURN p SKIP {0} LIMIT {1}""".format(
                skip, limit, version
            )
        data = self.run(query)
        return [self._parse_path(item["p"]) for item in data] if data else []

    def get_nodes(
        self, skip: int = 0, limit: int = 20, *labels: list, **properties: dict
    ) -> List[Node]:
        """根据节点属性获取节点

        :param skip: 偏移量
        :param limit: 返回数据量
        :param *labels: 标签
        :param *properties: 属性
        :return 节点列表
        :rtype: typing.List[models.Node]
        """
        return (
            self.neo4j_graph.nodes.match(*labels, **properties)
            .limit(limit)
            .skip(skip)
            .all()
        )

    def create_node(self, node: Node) -> Node:
        """创建node点

        :param node: node点数据
        :type node: Node
        :return: node点数据
        :rtype: Node
        """
        now_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        node.properties["_create_time"] = now_time
        node.properties["_update_time"] = now_time
        return self.neo4j_graph.create(node)

    def create_edge(self, edge: Edge) -> Edge:
        """创建边(会重复创建边)
        无论是否存在边，均会创建

        :param edge: 待创建边
        :type edge: models.Edge
        :return 创建好的边
        :rtype: models.Edge
        """
        now_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        properties = edge.properties
        properties["_create_time"] = now_time
        properties["_update_time"] = now_time
        properties["create_time"] = None
        properties["update_time"] = None
        return self.neo4j_graph.create(edge)

    def get_edge_by_node_name(
        self, labels, src_node_name: str, dest_node_name: str, relation_name: str
    ):
        match_query = f"MATCH (m:{':'.join(labels)})-[r]->(n:{':'.join(labels)}) "
        where_query1 = f"WHERE m.name='{src_node_name}' AND n.name='{dest_node_name}' "
        where_query2 = f"AND r.name='{relation_name}' "
        return_query = "return r"
        results = self.run(match_query + where_query1 + where_query2 + return_query)
        return self._parse_edge(results[0]["r"])

    def get_node_by_eid_with_labels(self, labels: List[str], eid: str):
        query = f"MATCH (m:{':'.join(labels)}) WHERE m._eid='{eid}' RETURN m"
        node = self.run(query)
        if len(node) == 0:
            raise ValueError("实体不存在")
        return self._parse_node(node[0]["m"])

    def delete_node_version_by_eid(self, label, _eid, version):
        """删除节点版本"""
        query = f"""MATCH (n:{label}) WHERE n._eid='{_eid}' REMOVE n:{version}"""
        self.run(query)

    def delete_relation_version_by_id(self, _id, version):
        """删除边版本"""
        query = f"""MATCH (m)-[r]-(n) WHERE id(r)={_id} SET r.{version}=null"""
        self.run(query)

    def get_all_onto_relations_by_labels(self, labels, ontology_name):
        match_query = f"MATCH p=(n:{':'.join(labels)})-[r]->(m:{':'.join(labels)}) "
        where_query = f'WHERE m.name="{ontology_name}" and r.name="属于" '
        query = f"""{match_query}{where_query} RETURN p"""
        results = self.run(query)
        return [self._parse_path(result[0]) for result in results]

    def get_all_relations_by_node(self, node):
        match_query = (
            f"MATCH p=(n:{':'.join(node.labels)})-[r]-(m:{':'.join(node.labels)}) "
        )
        where_query = f"WHERE n._eid='{node.properties.get('_eid')}' "
        query = f"""{match_query}{where_query} RETURN p"""
        results = self.run(query)
        return [self._parse_path(result[0]) for result in results]


class SearchMapper(metaclass=SingletonMeta):
    def __init__(self, configs: Dict) -> None:
        self.configs = configs
        conn = connections.Connections()
        self.client = conn.create_connection(
            hosts=[configs["host"]], alias="kg", timeout=80
        )
        self.indices_client = self.client.indices

    def create_entity(
        self,
        index_name: str,
        name: str,
        type_: str,
        project: str,
        id_: str,
        kg_id: int = None,
        node_type: str = None,
        version: str = "c",
    ):

        table_name = Linker
        now = datetime.datetime.now()
        entity = table_name(
            _id=id_,
            name=name,
            source_type=3,
            type=type_,
            status=1,
            created_at=now,
            updated_at=now,
            version=version,
            eid=id_,
            kg_id=kg_id,
            node_type=node_type,
        )
        entity.save(index=index_name, refresh=True, using=self.client)
