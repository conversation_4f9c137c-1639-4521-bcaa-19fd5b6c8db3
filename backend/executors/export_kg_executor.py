from typing import Dict

from asgiref.sync import sync_to_async
from django.db import close_old_connections
from docarray import DocumentArray
from yfflow import YfExecutor, requests
from yunfu.common import ConfigUtils
from yunfu.common.exceptions import ErrorCodes, YfException

from backend.models.kg import Kg, KgVersion, close_old_database_connections
from backend.modules.export import ExporterFactory, ExportTypes

conf = ConfigUtils.load('conf/config.yaml')


class ExportKgExecutor(YfExecutor):

    @requests(on='/export_kg')
    async def create(self, docs: DocumentArray, **kwargs: Dict[str, dict]) -> DocumentArray:
        for doc in docs:
            kg_id = doc.tags.get('kg_id')
            version_number = int(doc.tags.get('version_number', 0))
            org_id = doc.tags.get('org_id')
            if kg_id:
                await sync_to_async(self._export_kg)(int(kg_id), version_number, org_id)

    @close_old_database_connections
    def _export_kg(self, kg_id: int, version_number: int, org_id: str):
        self.logger.info(f'导出图谱: {kg_id}, {version_number}, {org_id}')
        kg = Kg.objects.filter(id=kg_id).first()
        if not kg:
            raise YfException(ErrorCodes.INVALID_PARAMETERS, "kg_id错误")
        kg_version = kg.versions.filter(number=version_number).first()
        version = KgVersion.number2str(version_number)
        for export_type in ExportTypes:
            file_id = ExporterFactory.get_exporter(export_type).export(kg_id, version, org_id)
            setattr(kg_version, f'{export_type.value}_file', file_id)
        close_old_connections()  # 如果图谱很大，处理时间长，这里的连接可能会断开，需要重新连接
        kg_version.save(update_fields=['entity_file', 'ontology_file', 'kg_file', 'full_file'])
