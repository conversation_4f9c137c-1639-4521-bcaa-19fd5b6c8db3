from typing import Dict

from asgiref.sync import sync_to_async
from docarray import DocumentArray
from yfflow import YfExecutor, requests

from backend.graph import get_graph_mapper
from backend.models.kg import Kg, close_old_database_connections
from backend.models.schemas import UpdateKbqaRequest
from backend.modules.update_kbqa import TriplesParser, export_triples, load_triples


class UpdateKbqaExecutor(YfExecutor):

    @requests(on="/update_kbqa")
    async def create(
        self, docs: DocumentArray, **kwargs: Dict[str, dict]
    ) -> DocumentArray:
        for doc in docs:
            params = UpdateKbqaRequest(**doc.tags)
            await sync_to_async(self._update_kbqa)(params)

    @close_old_database_connections
    def _update_kbqa(self, params: UpdateKbqaRequest) -> None:
        kg = Kg.objects.filter(id=params.kg_id).first()
        if not kg:
            raise ValueError("kg_id错误")
        graph_mapper = get_graph_mapper(kg.db, kg.db_config)
        triples = load_triples(graph_mapper, kg.space, params.version)
        parse_result = TriplesParser.parse(triples)
        export_triples(parse_result, params.kg_id, params.version)
