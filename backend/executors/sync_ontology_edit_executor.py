import json
import time
from typing import Dict

from asgiref.sync import sync_to_async
from docarray import DocumentArray
from yfflow import Client, YfExecutor, requests
from yunfu.common import ConfigUtils, yfid
from yunfu.common.exceptions import ErrorCodes, YfException
from yunfu.db.graph.models import Edge, Node

from backend.executors import SaveExecutor, UpdateKbqaExecutor
from backend.executors.update_ontology_tree_executor import UpdateOntologyTreeExecutor
from backend.graph import get_graph_mapper
from backend.mappers.graph_mapper import SearchMapper
from backend.models.kg import Kg, KgVersion, OntologyEditLog, ResourceChangeLog, close_old_database_connections
from backend.utils import GraphUtils, UuidUtils

conf = ConfigUtils.load("conf/config.yaml")


class SyncOntologyEditExecutor(YfExecutor):
    config = ConfigUtils.load("conf/config.yaml")
    search_mapper = SearchMapper(config["ES_CONFIGS"])
    index = config["ES_CONFIGS"]["save_index"]
    save_executor = SaveExecutor()
    update_kbqa_executor = UpdateKbqaExecutor()
    update_ontology_tree = UpdateOntologyTreeExecutor()
    count_client = Client(**conf["count_conf"])

    @requests(on="/sync_edit")
    async def create(
        self, docs: DocumentArray, **kwargs: Dict[str, dict]
    ) -> DocumentArray:
        for doc in docs:
            kg_id = doc.tags.get("kg_id")
            if kg_id:
                await sync_to_async(self._sync_edit)(int(kg_id))

    @close_old_database_connections
    def _sync_edit(self, kg_id: int) -> None:  # noqa: C901
        kg = Kg.objects.filter(id=kg_id).first()
        if not kg:
            raise YfException(ErrorCodes.INVALID_PARAMETERS, "kg_id错误")
        graph_mapper = get_graph_mapper(kg.db, kg.db_config)
        space = GraphUtils.get_label(kg_id)
        ontology_label = GraphUtils.get_ontology_label(kg_id)
        edit_logs = OntologyEditLog.objects.filter(kg_id=kg_id, status=1)
        labels = [ontology_label]
        # 设置图谱c版本为同步编辑操作中
        kg_version = kg.versions.filter(number=0).first()
        kg_version.sync_status = 2
        kg_version.save(update_fields=["sync_status"])
        for edit_log in edit_logs:
            try:
                if edit_log.operation == 1:
                    # 新增概念操作，不需要同步实体
                    node_name = edit_log.dst
                    self._sync_insert_ontology(graph_mapper, space, labels, kg_id, node_name)
                elif edit_log.operation == 2:
                    # 修改概念名称，需要同步修改实体_type属性值，删除节点分类标签
                    self._sync_modify_name(
                        graph_mapper,
                        space,
                        kg_id,
                        labels,
                        json.loads(edit_log.src),
                        json.loads(edit_log.dst),
                    )
                elif edit_log.operation == 3:
                    # 删除概念，需要同步删除该分类实体（只需要删除c版本标签即可）
                    self._sync_delete_ontology(graph_mapper, space, labels, edit_log.dst)
                elif edit_log.operation == 4:
                    # 新增属性，不需要同步到实体
                    self._sync_insert_property(graph_mapper, kg_id, labels, json.loads(edit_log.dst))
                elif edit_log.operation == 5:
                    # 修改属性，需要同步到实体
                    self._sync_modify_property(
                        graph_mapper,
                        space,
                        kg_id,
                        labels,
                        json.loads(edit_log.src),
                        json.loads(edit_log.dst),
                    )
                elif edit_log.operation == 6:
                    # 删除属性，需要同步到实体
                    self._sync_delete_property(graph_mapper, space, kg_id, labels, json.loads(edit_log.src))
                elif edit_log.operation == 7:
                    # 新增关系，不需要同步到实体
                    self._sync_insert_relation(graph_mapper, space, kg_id, labels, json.loads(edit_log.dst))
                elif edit_log.operation == 8:
                    # 修改关系，需要同步到实体
                    self._sync_modify_relation(
                        graph_mapper, space,
                        kg_id,
                        labels,
                        json.loads(edit_log.src),
                        json.loads(edit_log.dst),
                    )
                else:
                    # operation == 9 删除关系，需要同步到实体
                    self._sync_delete_relation(graph_mapper, space, kg_id, labels, json.loads(edit_log.src))
            except Exception:
                continue
            edit_log.status = 2
            edit_log.save()
        kg_version.sync_status = 1
        kg_version.save(update_fields=["sync_status"])
        # self.count_client.post("/kg_count", {"kg_id": kg_id, "version_number": 0})
        # self.count_client.post(
        #     "/update_center_node", {"kg_id": kg_id, "version_number": 0}
        # )
        # self.save_executor._reindex(kg_id, "c")
        # self.update_ontology_tree._update_tree(kg_id, 0)
        # self.update_kbqa_executor._update_kbqa(kg_id, "c")

    def _get_root_node_eid(self, index_name, graph_mapper, space, labels, kg_id, node_type) -> Node:
        node = graph_mapper.get_node_by_name(space, "c", labels, "事物")
        if not node:
            eid = yfid(UuidUtils.get_uuid("事物"))
            node = Node(
                id=eid,
                types=labels,
                props={
                    "name": "事物",
                    "_show_name": "事物",
                    "_eid": eid,
                    "_type": "概念",
                },
            )
            node = graph_mapper.insert_node(space, node)
            self.search_mapper.create_entity(
                index_name, node.name, "概念", "yfkm", eid, kg_id, node_type
            )
        return node

    def _sync_insert_ontology(self, graph_mapper, space, labels, kg_id, name) -> None:
        eid = yfid(UuidUtils.get_uuid(name))
        category_node = self._get_root_node_eid(self.index, graph_mapper, space, labels, kg_id, "ont")
        category = category_node.name
        node_ = Node(
            id=eid,
            types=labels,
            props={
                "name": name,
                "_eid": eid,
                "_type": category,
                "_show_name": name,
            },
        )
        node = graph_mapper.insert_node(space, node_)
        edge_source_id = yfid(f"属于{time.time()}")[:6]
        edge = Edge(
            id=edge_source_id,
            src_id=node.id,
            type="属于",
            dst_id=category_node.id,
            props={"name": "属于", "_rid": edge_source_id},
        )
        edge = graph_mapper.insert_edge(space, edge)
        self.search_mapper.create_entity(
            self.index, name, category, "yfkm", eid, int(kg_id), "ont"
        )
        logs = []
        log_entity = {
            "resource_name": node.name,
            "resource_id": node.props["_eid"],
            "resource_type": "entity",
            "operation": "create",
            "value": "",
            "source_id": "",
        }
        logs.append(log_entity)
        resource_id = f'{node.props["_eid"]}{category_node.props["_eid"]}{edge_source_id}'
        log_relation = {
            "resource_name": "属于",
            "resource_id": resource_id,
            "resource_type": "relation",
            "operation": "create",
            "value": "",
            "source_id": "",
        }
        logs.append(log_relation)
        ResourceChangeLog.create_logs(logs)

    def _modify_property(
        self, graph_mapper, space, kg_id, labels, name, old_property_name, property_name, property_value
    ) -> None:
        node = graph_mapper.get_node_by_name(space, "c", labels, name)
        if old_property_name == property_name:
            old_property_name = None
        else:
            if property_name in node.props and property_name != "name":
                raise YfException(
                    ErrorCodes.INVALID_PARAMETERS, f"{property_name}属性已存在"
                )
        if KgVersion.node_has_history(labels, kg_id):
            # TODO: 历史版本处理
            eid = node.props["_eid"]
            edges = graph_mapper.get_all_relations_by_node_with_labels(
                space, labels, eid
            )
            new_props = node.props
            if old_property_name:
                node.props[old_property_name] = None
                node.props[property_name] = property_value
                node = graph_mapper.insert_node(space, node)
                new_eid = yfid(UuidUtils.get_uuid(node.props["name"]))
                new_labels = [label for label in labels if label != "c"]
                new_props["_eid"] = new_eid
                new_node = Node(id=new_eid, types=new_labels, props=new_props)
                new_node = graph_mapper.insert_node(space, new_node)
                graph_mapper.delete_node_version_by_eid(
                    space, new_node.props["_eid"], "c"
                )
                for edge in edges:
                    if edge.src_node.props["_eid"] == eid:
                        new_edge = Edge(
                            src_id=new_node.id,
                            type=edge.type,
                            dst_id=edge.dst_node.id,
                            props=edge.props,
                        )
                    else:
                        new_edge = Edge(
                            src_id=edge.src_node.id,
                            type=edge.type,
                            dst_id=new_node.id,
                            props=edge.props,
                        )
                    new_edge = graph_mapper.insert_edge(space, new_edge)
                    graph_mapper.delete_relation_version_by_id(space, new_edge.id, "c")
        else:
            node.props[property_name] = property_value
            if old_property_name:
                node.props[old_property_name] = None
            node = graph_mapper.partial_update_node(space, node.id, node.props)

    def _insert_property(self, graph_mapper, kg_id, labels, name, property_name, property_value) -> None:
        space = f"KG{kg_id}"
        node = graph_mapper.get_node_by_name(space, "c", labels, name)
        if KgVersion.node_has_history(labels, kg_id):
            eid = node.props["_eid"]
            edges = graph_mapper.get_edges_with_eid(space, labels, eid)
            new_props = node.props
            node.props[property_name] = property_value
            node = graph_mapper.partial_update_node(space, node.id, node.props)
            new_eid = yfid(UuidUtils.get_uuid(node.props["name"]))
            new_labels = [label for label in labels if label != "c"]
            new_props["_eid"] = new_eid
            new_node = Node(id=new_eid, types=new_labels, props=new_props)
            new_node = graph_mapper.insert_node(space, new_node)
            graph_mapper.delete_node_version_by_eid(space, new_node.id, "c")
            for edge in edges:
                if edge.src_node.props["_eid"] == eid:
                    new_edge = Edge(
                        src_id=new_node.id,
                        type=edge.relation,
                        dst_id=edge.dest_node.id,
                        props=edge.props,
                    )
                else:
                    new_edge = Edge(
                        src_id=edge.src_node,
                        type=edge.relation,
                        dst_id=new_node,
                        props=edge.props,
                    )
                new_edge = graph_mapper.insert_edge(space, new_edge)
                graph_mapper.delete_relation_version_by_id(space, new_edge.id, "c")
        else:
            node.props[property_name] = property_value
            node = graph_mapper.partial_update_node(space, node.id, node.props)

    def _delete_property(self, graph_mapper, space, kg_id, labels, name, property_name) -> None:
        node = graph_mapper.get_nodes(space, 0, 1, labels, [("name", "=", name)])[0]
        if property_name == "节点类型":
            property_name = "_type"
        if KgVersion.node_has_history(labels, kg_id):
            eid = node.props["_eid"]
            edges = graph_mapper.get_edges_with_eid(space, labels, eid)
            new_props = dict(node.props)
            node.props[property_name] = None
            node = graph_mapper.insert_node(space, node)
            new_eid = yfid(UuidUtils.get_uuid(node.props["name"]))
            new_labels = [label for label in labels if label != "c"]
            new_props["_eid"] = new_eid
            new_node = Node(id=new_eid, types=new_labels, props=new_props)
            new_node = graph_mapper.insert_node(space, new_node)
            graph_mapper.delete_node_version_by_eid(
                space, new_node.props["_eid"], "c"
            )
            for edge in edges:
                if edge.src_node.props["_eid"] == eid:
                    new_edge = Edge(
                        src_id=new_node.id,
                        type=edge.type,
                        dst_id=edge.dst_node.id,
                        props=edge.props,
                    )
                else:
                    new_edge = Edge(
                        src_id=edge.src_node.id,
                        type=edge.type,
                        dst_id=new_node.id,
                        props=edge.props,
                    )
                new_edge = graph_mapper.insert_edge(space, new_edge)
                graph_mapper.delete_relation_version_by_id(space, new_edge.id, "c")
        else:
            node.props[property_name] = None
            node = graph_mapper.partial_update_node(space, node.id, node.props)

    def _insert_relation(
        self, graph_mapper, space, kg_id, labels, src_name, dest_name, direction, relation_name
    ):
        node1 = graph_mapper.get_nodes(space, 0, 1, labels, [("name", "=", src_name)])[0]
        node2 = graph_mapper.get_nodes(space, 0, 1, labels, [("name", "=", dest_name)])[0]
        relation = relation_name if relation_name == "属于" else "关联"
        edge_source_id = yfid(f"{relation_name}{time.time()}")[:6]
        if direction == 2:
            edge = Edge(
                src_id=node2.id,
                type=relation,
                dst_id=node1.id,
                props={
                    "name": relation_name,
                    "_rid": edge_source_id,
                },
            )
        else:
            edge = Edge(
                src_id=node1.id,
                type=relation,
                dst_id=node2.id,
                props={
                    "name": relation_name,
                    "_rid": edge_source_id,
                },
            )
        graph_mapper.insert_edge(space, edge)
        logs = []
        # 获取节点的eid用于日志记录
        src_eid = node1.props["_eid"] if direction != 2 else node2.props["_eid"]
        dst_eid = node2.props["_eid"] if direction != 2 else node1.props["_eid"]
        resource_id = f'{src_eid}{dst_eid}{edge_source_id}'
        log_relation = {
            "resource_name": relation_name,
            "resource_id": resource_id,
            "resource_type": "relation",
            "operation": "create",
            "value": "",
            "source_id": "",
        }
        logs.append(log_relation)
        ResourceChangeLog.create_logs(logs)

    def _modify_relation(
        self, graph_mapper, space, kg_id, labels, src_name, dest_name, old_relation_name, relation_name
    ):
        relation = relation_name if relation_name == "属于" else "关联"
        edge = graph_mapper.get_edge_by_node_name(
            space, src_name, dest_name, old_relation_name
        )
        if KgVersion.relation_has_history(edge.props, kg_id):
            new_props = dict(edge.props)
            edge.props["name"] = relation_name
            for _property in edge.props:
                if edge.props[_property] is True and _property != "c":
                    edge.props[_property] = None
            graph_mapper.partial_update_edge(space, edge.id, edge.props)
            edge_source_id = yfid(f'{edge.props["name"]}{time.time()}')[:6]
            new_props["_rid"] = edge_source_id
            new_edge = Edge(
                src_id=edge.src_id,
                type=relation,
                dst_id=edge.dst_id,
                props=new_props,
            )
            new_edge = graph_mapper.insert_edge(space, new_edge)
            graph_mapper.delete_relation_version_by_id(space, new_edge.id, "c")
        else:
            edge.props["name"] = relation_name
            graph_mapper.partial_update_edge(space, edge.id, edge.props)

    def _delete_relation(self, graph_mapper, space, kg_id, labels, src_name, dest_name, relation_name):
        edge = graph_mapper.get_edge_by_node_name(
            space, src_name, dest_name, relation_name
        )
        relation_id = edge.id
        graph_mapper.delete_relation_version_by_id(space, int(relation_id), "c")

    def _sync_modify_name(self, graph_mapper, space, kg_id, labels, edit_src, edit_dst):
        node_name = edit_src["name"]
        old_property_name = edit_src["old_property_name"]
        property_name = edit_dst["property_name"]
        property_value = edit_dst["property_value"]
        # 同步操作到本体
        self._modify_property(
            graph_mapper, space, kg_id, labels, node_name,
            old_property_name, property_name, property_value
        )
        # 同步操作到实体
        graph_mapper.sync_entity_type_by_ontology_name(space, kg_id, labels, node_name, property_value)

    def _sync_delete_ontology(self, graph_mapper, space, labels, name):
        # 先同步操作到实体（先同步本体会导致无法获取c版本对应实体与本体的关系）
        graph_mapper.delete_node_by_ontology(space, labels, name)
        # 同步操作到本体
        graph_mapper.delete_node_by_name(space, labels, name)

    def _sync_insert_property(self, graph_mapper, kg_id, labels, edit_dst):
        node_name = edit_dst["name"]
        property_name = edit_dst["property_name"]
        property_value = edit_dst["property_value"]
        # 同步操作到本体
        self._insert_property(graph_mapper, kg_id, labels, node_name, property_name, property_value)

    def _sync_modify_property(self, graph_mapper, space, kg_id, labels, edit_src, edit_dst):
        node_name = edit_src["name"]
        old_property_name = edit_src["old_property_name"]
        property_name = edit_dst["property_name"]
        property_value = edit_dst["property_value"]
        # 同步操作到本体
        self._modify_property(
            graph_mapper, space, kg_id, labels, node_name, old_property_name, property_name, property_value
        )
        # 同步操作到实体
        graph_mapper.sync_entity_property_rename(space, labels, node_name, old_property_name, property_name)

    def _sync_delete_property(self, graph_mapper, space, kg_id, labels, edit_src):
        node_name = edit_src["name"]
        property_name = edit_src["property_name"]
        # 同步操作到本体
        self._delete_property(graph_mapper, space, kg_id, labels, node_name, property_name)
        # 同步操作到实体，删除所有对应属性
        graph_mapper.sync_entity_property_remove(space, labels, node_name, property_name)

    def _sync_insert_relation(self, graph_mapper, space, kg_id, labels, edit_dst):
        src_node_name = edit_dst["name"]
        dest_node_name = edit_dst["second_name"]
        direction = edit_dst["direction"]
        relation_name = edit_dst["relation_name"]
        self._insert_relation(
            graph_mapper, space, kg_id, labels, src_node_name, dest_node_name, direction, relation_name
        )

    def _sync_modify_relation(self, graph_mapper, space, kg_id, labels, edit_src, edit_dst):
        src_name = edit_src["src_name"]
        dest_name = edit_dst["dest_name"]
        old_relation_name = edit_src["old_relation_name"]
        relation_name = edit_dst["relation_name"]
        # 同步操作到本体
        self._modify_relation(
            graph_mapper, space, kg_id, labels, src_name, dest_name, old_relation_name, relation_name
        )
        # 同步操作到实体关系（删除边c=true的属性）
        graph_mapper.sync_entity_relation_rename(space, kg_id, src_name, dest_name, old_relation_name, relation_name)

    def _sync_delete_relation(self, graph_mapper, space, kg_id, labels, edit_src):
        src_name = edit_src["src_name"]
        dest_name = edit_src["dest_name"]
        relation_name = edit_src["relation_name"]
        # 同步操作到本体
        self._delete_relation(graph_mapper, space, kg_id, labels, src_name, dest_name, relation_name)
        # 同步操作到实体关系（删除边c=true的属性）
        graph_mapper.sync_entity_relation_remove(space, kg_id, src_name, dest_name, relation_name)
