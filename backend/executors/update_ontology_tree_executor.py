from typing import Dict

from asgiref.sync import sync_to_async
from docarray import DocumentArray
from yfflow import YfExecutor, requests
from yunfu.common import ConfigUtils
from yunfu.common.exceptions import ErrorCodes, YfException

from backend.graph import get_graph_mapper
from backend.mappers.graph_mapper import SearchMapper
from backend.models.kg import Kg, KgVersion, close_old_database_connections
from backend.utils import GraphUtils, NodeColorManager

conf = ConfigUtils.load('conf/config.yaml')


class UpdateOntologyTreeExecutor(YfExecutor):
    config = ConfigUtils.load('conf/config.yaml')
    search_mapper = SearchMapper(config['ES_CONFIGS'])

    @requests(on='/ontology_tree')
    async def create(self, docs: DocumentArray, **kwargs: Dict[str, dict]) -> DocumentArray:
        for doc in docs:
            kg_id = doc.tags.get('kg_id')
            version_number = doc.tags.get('version_number', 0)
            if kg_id:
                await sync_to_async(self._update_tree)(int(kg_id), int(version_number))

    @close_old_database_connections
    def _update_tree(self, kg_id: int, version_number: int) -> None:  # noqa: C901
        kg = Kg.objects.filter(id=kg_id).first()
        if not kg:
            raise YfException(ErrorCodes.INVALID_PARAMETERS, "kg_id错误")
        self.logger.info(f'create ontology_tree in kg: {kg_id} version: {version_number}')
        graph_mapper = get_graph_mapper(kg.db, kg.db_config)
        space = GraphUtils.get_label(kg_id)
        ontology = '事物'
        labels = ['concept']
        version = KgVersion.number2str(version_number)
        relations = graph_mapper.get_all_onto_relations_by_labels(space, version, labels, ontology)
        ontology_tree = self.recursion_children(graph_mapper, space, version, labels, relations, [])
        kg_version = kg.versions.filter(number=version_number).first()
        kg_version.ontology_tree = ontology_tree
        kg_version.save(update_fields=['ontology_tree'])

    def recursion_children(self, graph_mapper, space, version, labels, child_rel, relation_strings):
        children = []
        for child in child_rel:
            inverse_relation = f'{child.dst_node.name}_{child.src_node.name}'
            if inverse_relation in relation_strings:
                continue
            relation_strings.append(f'{child.src_node.name}_{child.dst_node.name}')
            child_relation = graph_mapper.get_all_onto_relations_by_labels(space, version, labels, child.src_node.name)
            next_children = self.recursion_children(
                graph_mapper, space, version, labels, child_relation, relation_strings)
            children.append({
                'label': child.src_node.name,
                'eid': child.src_node.props['_eid'],
                'has_children': True if next_children else False,
                'children': next_children
            })
        name2color = NodeColorManager.get_node2color([child['label'] for child in children])
        for child in children:
            child['color'] = name2color[child['label']]
        # 对ontologies进行排序，按label的升序
        children = sorted(children, key=lambda x: x['label'])
        return children
