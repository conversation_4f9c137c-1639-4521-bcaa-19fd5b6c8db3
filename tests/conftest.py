import pytest

from backend.graph import NebulaGraphMapper, Neo4jGraphMapper


@pytest.fixture(scope="session")
def nebula_space():
    return "KG125276"


@pytest.fixture(scope="session")
def neo4j_space():
    return "KG125279"


@pytest.fixture(scope="session")
def nebula_graph_config():
    return {
        "host": "*************",
        "port": 9669,
        "username": "root",
        "password": "root",
    }


@pytest.fixture(scope="session")
def neo4j_graph_config():
    return {
        "host": "*************",
        "port": 20873,
        "username": "neo4j",
        "password": "yunfu2017",
    }


@pytest.fixture(scope="session")
def nebula_mapper(nebula_graph_config):
    """创建NebulaGraphMapper实例"""
    return NebulaGraphMapper(nebula_graph_config, enable_version=True)


@pytest.fixture(scope="session")
def neo4j_mapper(neo4j_graph_config):
    """创建Neo4jGraphMapper实例"""
    return Neo4jGraphMapper(neo4j_graph_config, enable_version=True)


@pytest.fixture(scope="session")
def test_version():
    """测试版本号"""
    return "c"
